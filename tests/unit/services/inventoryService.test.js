// Unit tests for inventoryService.js
const inventoryService = require('../../../services/inventoryService');
const testData = require('../../fixtures/testData');

// Mock the inventory cache
jest.mock('../../../cache/inventoryCache', () => new Map());

describe('InventoryService', () => {
  let mockInventoryCache;

  beforeEach(() => {
    // Get the mocked cache instance
    mockInventoryCache = require('../../../cache/inventoryCache');

    // Clear the cache before each test
    mockInventoryCache.clear();

    // Clear all mocks
    jest.clearAllMocks();

    // Mock Date.now() for consistent ID generation
    jest.spyOn(Date, 'now').mockReturnValue(1234567890);
  });

  afterEach(() => {
    // Restore Date.now() if it was mocked
    if (Date.now.mockRestore) {
      Date.now.mockRestore();
    }
  });

  describe('createItem', () => {
    it('should successfully create a new item', () => {
      // Arrange
      const { name, quantity } = testData.items.validItem;
      const expectedId = '1234567890';

      // Act
      const result = inventoryService.createItem(name, quantity);

      // Assert
      expect(result).toEqual({
        message: 'Item added',
        id: expectedId
      });
      expect(mockInventoryCache.has(expectedId)).toBe(true);
      expect(mockInventoryCache.get(expectedId)).toEqual({
        name,
        quantity
      });
    });

    it('should create item with zero quantity', () => {
      // Arrange
      const { name, quantity } = testData.items.itemWithZeroQuantity;
      const expectedId = '1234567890';

      // Act
      const result = inventoryService.createItem(name, quantity);

      // Assert
      expect(result).toEqual({
        message: 'Item added',
        id: expectedId
      });
      expect(mockInventoryCache.get(expectedId).quantity).toBe(0);
    });

    it('should create item with empty name', () => {
      // Arrange
      const name = '';
      const quantity = 10;
      const expectedId = '1234567890';

      // Act
      const result = inventoryService.createItem(name, quantity);

      // Assert
      expect(result).toEqual({
        message: 'Item added',
        id: expectedId
      });
      expect(mockInventoryCache.get(expectedId).name).toBe('');
    });

    it('should create item with negative quantity', () => {
      // Arrange
      const name = 'Test Item';
      const quantity = -5;
      const expectedId = '1234567890';

      // Act
      const result = inventoryService.createItem(name, quantity);

      // Assert
      expect(result).toEqual({
        message: 'Item added',
        id: expectedId
      });
      expect(mockInventoryCache.get(expectedId).quantity).toBe(-5);
    });

    it('should generate unique IDs for multiple items', () => {
      // Arrange
      if (Date.now.mockRestore) {
        Date.now.mockRestore(); // Use real Date.now() for this test
      }
      const { name, quantity } = testData.items.validItem;

      // Act
      const result1 = inventoryService.createItem(name, quantity);
      // Add a small delay to ensure different timestamps
      jest.spyOn(Date, 'now').mockReturnValue(1234567891);
      const result2 = inventoryService.createItem(name, quantity);

      // Assert
      expect(result1.id).not.toBe(result2.id);
      expect(mockInventoryCache.size).toBe(2);
    });
  });

  describe('getItems', () => {
    it('should return empty array when no items exist', () => {
      // Act
      const result = inventoryService.getItems();

      // Assert
      expect(result).toEqual([]);
    });

    it('should return single item with id', () => {
      // Arrange
      const itemId = '123';
      const itemData = testData.items.validItem;
      mockInventoryCache.set(itemId, itemData);

      // Act
      const result = inventoryService.getItems();

      // Assert
      expect(result).toEqual([{
        id: itemId,
        ...itemData
      }]);
    });

    it('should return multiple items with their ids', () => {
      // Arrange
      const items = [
        { id: '1', data: { name: 'Item 1', quantity: 10 } },
        { id: '2', data: { name: 'Item 2', quantity: 20 } },
        { id: '3', data: { name: 'Item 3', quantity: 5 } }
      ];

      items.forEach(item => {
        mockInventoryCache.set(item.id, item.data);
      });

      // Act
      const result = inventoryService.getItems();

      // Assert
      expect(result).toHaveLength(3);
      expect(result).toEqual(expect.arrayContaining([
        { id: '1', name: 'Item 1', quantity: 10 },
        { id: '2', name: 'Item 2', quantity: 20 },
        { id: '3', name: 'Item 3', quantity: 5 }
      ]));
    });

    it('should maintain item order from cache iteration', () => {
      // Arrange
      mockInventoryCache.set('first', { name: 'First Item', quantity: 1 });
      mockInventoryCache.set('second', { name: 'Second Item', quantity: 2 });

      // Act
      const result = inventoryService.getItems();

      // Assert
      expect(result[0].id).toBe('first');
      expect(result[1].id).toBe('second');
    });
  });

  describe('updateItem', () => {
    beforeEach(() => {
      // Set up existing items
      mockInventoryCache.set('existing-id', {
        name: 'Original Item',
        quantity: 5
      });
    });

    it('should successfully update existing item', () => {
      // Arrange
      const itemId = 'existing-id';
      const { name, quantity } = testData.items.validItemUpdate;

      // Act
      const result = inventoryService.updateItem(itemId, name, quantity);

      // Assert
      expect(result).toEqual({ message: 'Item updated' });
      expect(mockInventoryCache.get(itemId)).toEqual({
        name,
        quantity
      });
    });

    it('should throw error when item does not exist', () => {
      // Arrange
      const nonExistentId = 'non-existent-id';
      const { name, quantity } = testData.items.validItemUpdate;

      // Act & Assert
      expect(() => inventoryService.updateItem(nonExistentId, name, quantity))
        .toThrow('Item not found');
    });

    it('should update item with zero quantity', () => {
      // Arrange
      const itemId = 'existing-id';
      const name = 'Updated Item';
      const quantity = 0;

      // Act
      const result = inventoryService.updateItem(itemId, name, quantity);

      // Assert
      expect(result).toEqual({ message: 'Item updated' });
      expect(mockInventoryCache.get(itemId).quantity).toBe(0);
    });

    it('should update item with negative quantity', () => {
      // Arrange
      const itemId = 'existing-id';
      const name = 'Updated Item';
      const quantity = -10;

      // Act
      const result = inventoryService.updateItem(itemId, name, quantity);

      // Assert
      expect(result).toEqual({ message: 'Item updated' });
      expect(mockInventoryCache.get(itemId).quantity).toBe(-10);
    });

    it('should update item with empty name', () => {
      // Arrange
      const itemId = 'existing-id';
      const name = '';
      const quantity = 15;

      // Act
      const result = inventoryService.updateItem(itemId, name, quantity);

      // Assert
      expect(result).toEqual({ message: 'Item updated' });
      expect(mockInventoryCache.get(itemId).name).toBe('');
    });
  });

  describe('deleteItem', () => {
    beforeEach(() => {
      // Set up existing items
      mockInventoryCache.set('existing-id', {
        name: 'Item to Delete',
        quantity: 10
      });
      mockInventoryCache.set('another-id', {
        name: 'Another Item',
        quantity: 5
      });
    });

    it('should successfully delete existing item', () => {
      // Arrange
      const itemId = 'existing-id';
      const initialSize = mockInventoryCache.size;

      // Act
      const result = inventoryService.deleteItem(itemId);

      // Assert
      expect(result).toEqual({ message: 'Item deleted' });
      expect(mockInventoryCache.has(itemId)).toBe(false);
      expect(mockInventoryCache.size).toBe(initialSize - 1);
    });

    it('should throw error when item does not exist', () => {
      // Arrange
      const nonExistentId = 'non-existent-id';

      // Act & Assert
      expect(() => inventoryService.deleteItem(nonExistentId))
        .toThrow('Item not found');
    });

    it('should not affect other items when deleting', () => {
      // Arrange
      const itemToDelete = 'existing-id';
      const itemToKeep = 'another-id';
      const originalItem = mockInventoryCache.get(itemToKeep);

      // Act
      inventoryService.deleteItem(itemToDelete);

      // Assert
      expect(mockInventoryCache.has(itemToKeep)).toBe(true);
      expect(mockInventoryCache.get(itemToKeep)).toEqual(originalItem);
    });

    it('should handle deleting from empty cache gracefully', () => {
      // Arrange
      mockInventoryCache.clear();
      const nonExistentId = 'any-id';

      // Act & Assert
      expect(() => inventoryService.deleteItem(nonExistentId))
        .toThrow('Item not found');
    });
  });
});
