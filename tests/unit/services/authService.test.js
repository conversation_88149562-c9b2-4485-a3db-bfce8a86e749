// Unit tests for authService.js
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const authService = require('../../../services/authService');
const testData = require('../../fixtures/testData');

// Mock dependencies
jest.mock('bcryptjs');
jest.mock('jsonwebtoken');

// Mock cache modules
jest.mock('../../../cache/userCache', () => new Map());
jest.mock('../../../cache/resetTokenCache', () => new Map());

describe('AuthService', () => {
  let mockUserCache;
  let mockResetTokenCache;

  beforeEach(() => {
    // Get the mocked cache instances
    mockUserCache = require('../../../cache/userCache');
    mockResetTokenCache = require('../../../cache/resetTokenCache');

    // Clear all mocks and caches before each test
    jest.clearAllMocks();
    mockUserCache.clear();
    mockResetTokenCache.clear();

    // Set up default environment
    process.env.JWT_SECRET = 'test-secret-key';
  });

  describe('registerUser', () => {
    it('should successfully register a new user', async () => {
      // Arrange
      const { username, password, role } = testData.users.validUser;
      const hashedPassword = 'hashedPassword123';
      
      bcrypt.hash.mockResolvedValue(hashedPassword);

      // Act
      const result = await authService.registerUser(username, password, role);

      // Assert
      expect(bcrypt.hash).toHaveBeenCalledWith(password, 10);
      expect(mockUserCache.has(username)).toBe(true);
      expect(mockUserCache.get(username)).toEqual({
        password: hashedPassword,
        role: role
      });
      expect(result).toEqual({ message: 'User registered successfully' });
    });

    it('should throw error when username is missing', async () => {
      // Arrange
      const { password, role } = testData.users.validUser;

      // Act & Assert
      await expect(authService.registerUser('', password, role))
        .rejects.toThrow('All fields required: username, password, role');
    });

    it('should throw error when password is missing', async () => {
      // Arrange
      const { username, role } = testData.users.validUser;

      // Act & Assert
      await expect(authService.registerUser(username, '', role))
        .rejects.toThrow('All fields required: username, password, role');
    });

    it('should throw error when role is missing', async () => {
      // Arrange
      const { username, password } = testData.users.validUser;

      // Act & Assert
      await expect(authService.registerUser(username, password, ''))
        .rejects.toThrow('All fields required: username, password, role');
    });

    it('should throw error when user already exists', async () => {
      // Arrange
      const { username, password, role } = testData.users.validUser;
      mockUserCache.set(username, { password: 'existingPassword', role: 'user' });

      // Act & Assert
      await expect(authService.registerUser(username, password, role))
        .rejects.toThrow('User already exists');
    });

    it('should handle bcrypt hashing errors', async () => {
      // Arrange
      const { username, password, role } = testData.users.validUser;
      const hashError = new Error('Hashing failed');
      
      bcrypt.hash.mockRejectedValue(hashError);

      // Act & Assert
      await expect(authService.registerUser(username, password, role))
        .rejects.toThrow('Hashing failed');
    });
  });

  describe('loginUser', () => {
    beforeEach(() => {
      // Set up a test user in cache
      const hashedPassword = 'hashedPassword123';
      mockUserCache.set('testuser', {
        password: hashedPassword,
        role: 'user'
      });
    });

    it('should successfully login with valid credentials', async () => {
      // Arrange
      const { username, password } = testData.auth.validLoginCredentials;
      const expectedToken = 'jwt.token.here';
      
      bcrypt.compare.mockResolvedValue(true);
      jwt.sign.mockReturnValue(expectedToken);

      // Act
      const result = await authService.loginUser(username, password);

      // Assert
      expect(bcrypt.compare).toHaveBeenCalledWith(password, 'hashedPassword123');
      expect(jwt.sign).toHaveBeenCalledWith(
        { username, role: 'user' },
        process.env.JWT_SECRET || 'mysupersecretkey',
        { expiresIn: '1h' }
      );
      expect(result).toEqual({ token: expectedToken });
    });

    it('should throw error for non-existent user', async () => {
      // Arrange
      const { password } = testData.auth.validLoginCredentials;
      const nonExistentUsername = 'nonexistent';

      // Act & Assert
      await expect(authService.loginUser(nonExistentUsername, password))
        .rejects.toThrow('Invalid credentials');
    });

    it('should throw error for incorrect password', async () => {
      // Arrange
      const { username } = testData.auth.validLoginCredentials;
      const wrongPassword = 'wrongpassword';
      
      bcrypt.compare.mockResolvedValue(false);

      // Act & Assert
      await expect(authService.loginUser(username, wrongPassword))
        .rejects.toThrow('Invalid credentials');
    });

    it('should handle bcrypt comparison errors', async () => {
      // Arrange
      const { username, password } = testData.auth.validLoginCredentials;
      const compareError = new Error('Comparison failed');
      
      bcrypt.compare.mockRejectedValue(compareError);

      // Act & Assert
      await expect(authService.loginUser(username, password))
        .rejects.toThrow('Comparison failed');
    });
  });

  describe('generateResetToken', () => {
    beforeEach(() => {
      // Set up a test user in cache
      mockUserCache.set('testuser', {
        password: 'hashedPassword123',
        role: 'user'
      });
    });

    it('should generate reset token for existing user', () => {
      // Arrange
      const username = 'testuser';

      // Act
      const result = authService.generateResetToken(username);

      // Assert
      expect(result.message).toBe('Reset token generated');
      expect(result.resetToken).toBeGreaterThanOrEqual(100000);
      expect(result.resetToken).toBeLessThanOrEqual(999999);
      expect(mockResetTokenCache.has(username)).toBe(true);
      expect(mockResetTokenCache.get(username)).toBe(result.resetToken);
    });

    it('should throw error for non-existent user', () => {
      // Arrange
      const nonExistentUsername = 'nonexistent';

      // Act & Assert
      expect(() => authService.generateResetToken(nonExistentUsername))
        .toThrow('User does not exist');
    });

    it('should generate different tokens for multiple calls', () => {
      // Arrange
      const username = 'testuser';

      // Act
      const result1 = authService.generateResetToken(username);
      const result2 = authService.generateResetToken(username);

      // Assert
      // Note: There's a small chance they could be the same, but very unlikely
      expect(result1.resetToken).not.toBe(result2.resetToken);
    });
  });

  describe('resetPassword', () => {
    beforeEach(() => {
      // Set up a test user and reset token
      mockUserCache.set('testuser', {
        password: 'oldHashedPassword',
        role: 'user'
      });
      mockResetTokenCache.set('testuser', 123456);
    });

    it('should successfully reset password with valid token', async () => {
      // Arrange
      const username = 'testuser';
      const resetToken = '123456';
      const newPassword = 'newPassword123';
      const newHashedPassword = 'newHashedPassword123';

      bcrypt.hash.mockResolvedValue(newHashedPassword);

      // Act
      const result = await authService.resetPassword(username, resetToken, newPassword);

      // Assert
      expect(bcrypt.hash).toHaveBeenCalledWith(newPassword, 10);
      expect(mockUserCache.get(username).password).toBe(newHashedPassword);
      expect(mockResetTokenCache.has(username)).toBe(false);
      expect(result).toEqual({ message: 'Password reset successful' });
    });

    it('should throw error for non-existent user', async () => {
      // Arrange
      const nonExistentUsername = 'nonexistent';
      const resetToken = '123456';
      const newPassword = 'newPassword123';

      // Act & Assert
      await expect(authService.resetPassword(nonExistentUsername, resetToken, newPassword))
        .rejects.toThrow('User does not exist');
    });

    it('should throw error for invalid reset token', async () => {
      // Arrange
      const username = 'testuser';
      const invalidToken = '999999';
      const newPassword = 'newPassword123';

      // Act & Assert
      await expect(authService.resetPassword(username, invalidToken, newPassword))
        .rejects.toThrow('Invalid reset token');
    });

    it('should handle string reset token correctly', async () => {
      // Arrange
      const username = 'testuser';
      const resetToken = '123456'; // String version
      const newPassword = 'newPassword123';
      const newHashedPassword = 'newHashedPassword123';

      bcrypt.hash.mockResolvedValue(newHashedPassword);

      // Act
      const result = await authService.resetPassword(username, resetToken, newPassword);

      // Assert
      expect(result).toEqual({ message: 'Password reset successful' });
    });

    it('should handle bcrypt hashing errors during reset', async () => {
      // Arrange
      const username = 'testuser';
      const resetToken = '123456';
      const newPassword = 'newPassword123';
      const hashError = new Error('Hashing failed');

      bcrypt.hash.mockRejectedValue(hashError);

      // Act & Assert
      await expect(authService.resetPassword(username, resetToken, newPassword))
        .rejects.toThrow('Hashing failed');
    });

    it('should preserve user role when resetting password', async () => {
      // Arrange
      const username = 'testuser';
      const resetToken = '123456';
      const newPassword = 'newPassword123';
      const newHashedPassword = 'newHashedPassword123';
      const originalRole = 'user';

      bcrypt.hash.mockResolvedValue(newHashedPassword);

      // Act
      await authService.resetPassword(username, resetToken, newPassword);

      // Assert
      expect(mockUserCache.get(username).role).toBe(originalRole);
    });
  });
});
