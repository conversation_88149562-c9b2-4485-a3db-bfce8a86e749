// Database mock for testing
// This file provides mock implementations for database operations

// Mock PostgreSQL pool
const mockPool = {
  query: jest.fn(),
  connect: jest.fn(),
  end: jest.fn()
};

// Mock database responses
const mockDbResponses = {
  // User-related responses
  createUser: {
    rows: [{
      id: 1,
      username: 'testuser',
      password: '$2b$10$hashedpassword',
      role: 'user',
      created_at: new Date()
    }]
  },
  
  getUserByUsername: {
    rows: [{
      id: 1,
      username: 'testuser',
      password: '$2b$10$hashedpassword',
      role: 'user'
    }]
  },
  
  // Inventory-related responses
  createItem: {
    rows: [{
      id: 1,
      name: 'Test Item',
      quantity: 10,
      created_at: new Date(),
      updated_at: new Date()
    }]
  },
  
  getItemById: {
    rows: [{
      id: 1,
      name: 'Test Item',
      quantity: 10,
      created_at: new Date(),
      updated_at: new Date()
    }]
  },
  
  getAllItems: {
    rows: [
      {
        id: 1,
        name: 'Test Item 1',
        quantity: 10,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: 2,
        name: 'Test Item 2',
        quantity: 5,
        created_at: new Date(),
        updated_at: new Date()
      }
    ]
  },
  
  updateItem: {
    rows: [{
      id: 1,
      name: 'Updated Item',
      quantity: 15,
      created_at: new Date(),
      updated_at: new Date()
    }]
  },
  
  deleteItem: {
    rows: [],
    rowCount: 1
  },
  
  // Empty responses
  empty: {
    rows: [],
    rowCount: 0
  },
  
  // Database connection test
  connectionTest: {
    rows: [{ now: new Date() }]
  }
};

// Helper functions to set up database mocks
const dbMockHelpers = {
  // Mock successful database operations
  mockSuccessfulQuery: (response) => {
    mockPool.query.mockResolvedValue(response);
  },
  
  // Mock database errors
  mockDatabaseError: (error = new Error('Database connection failed')) => {
    mockPool.query.mockRejectedValue(error);
  },
  
  // Mock specific query responses
  mockQueryResponse: (query, response) => {
    mockPool.query.mockImplementation((sql, params) => {
      if (sql.includes(query)) {
        return Promise.resolve(response);
      }
      return Promise.resolve(mockDbResponses.empty);
    });
  },
  
  // Reset all mocks
  resetMocks: () => {
    mockPool.query.mockReset();
    mockPool.connect.mockReset();
    mockPool.end.mockReset();
  },
  
  // Get mock call history
  getQueryCalls: () => mockPool.query.mock.calls,
  
  // Verify query was called with specific parameters
  verifyQueryCalled: (expectedSql, expectedParams) => {
    const calls = mockPool.query.mock.calls;
    return calls.some(call => 
      call[0].includes(expectedSql) && 
      (expectedParams ? JSON.stringify(call[1]) === JSON.stringify(expectedParams) : true)
    );
  }
};

module.exports = {
  mockPool,
  mockDbResponses,
  dbMockHelpers
};
