// General test helpers and utilities

const testHelpers = {
  // Mock Express request object
  createMockRequest: (overrides = {}) => {
    return {
      body: {},
      params: {},
      query: {},
      headers: {},
      user: null,
      ...overrides
    };
  },
  
  // Mock Express response object
  createMockResponse: () => {
    const res = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
      send: jest.fn().mockReturnThis(),
      cookie: jest.fn().mockReturnThis(),
      clearCookie: jest.fn().mockReturnThis(),
      redirect: jest.fn().mockReturnThis(),
      render: jest.fn().mockReturnThis()
    };
    return res;
  },
  
  // Mock next function for middleware
  createMockNext: () => jest.fn(),
  
  // Wait for async operations
  wait: (ms = 100) => new Promise(resolve => setTimeout(resolve, ms)),
  
  // Generate random test data
  generateRandomString: (length = 10) => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  },
  
  generateRandomNumber: (min = 1, max = 100) => {
    return Math.floor(Math.random() * (max - min + 1)) + min;
  },
  
  // Test data generators
  generateTestUser: (overrides = {}) => ({
    username: testHelpers.generateRandomString(8),
    password: 'testpassword123',
    role: 'user',
    ...overrides
  }),
  
  generateTestItem: (overrides = {}) => ({
    name: `Test Item ${testHelpers.generateRandomString(5)}`,
    quantity: testHelpers.generateRandomNumber(1, 50),
    ...overrides
  }),
  
  // Validation helpers
  isValidJWT: (token) => {
    const jwtRegex = /^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/;
    return jwtRegex.test(token);
  },
  
  isValidEmail: (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  },
  
  // Mock console methods for testing
  mockConsole: () => {
    const originalConsole = { ...console };
    console.log = jest.fn();
    console.error = jest.fn();
    console.warn = jest.fn();
    console.info = jest.fn();
    
    return {
      restore: () => {
        Object.assign(console, originalConsole);
      }
    };
  },
  
  // Error testing helpers
  expectAsyncError: async (asyncFn, expectedError) => {
    try {
      await asyncFn();
      throw new Error('Expected function to throw an error');
    } catch (error) {
      if (expectedError) {
        expect(error.message).toContain(expectedError);
      }
      return error;
    }
  },
  
  // Mock cache implementations
  createMockCache: (initialData = {}) => {
    const cache = new Map();
    
    // Add initial data
    Object.entries(initialData).forEach(([key, value]) => {
      cache.set(key, value);
    });
    
    return cache;
  },
  
  // Response validation helpers
  expectSuccessResponse: (response, expectedData = null) => {
    expect(response.status).toHaveBeenCalledWith(200);
    expect(response.json).toHaveBeenCalled();
    
    if (expectedData) {
      const jsonCall = response.json.mock.calls[0][0];
      expect(jsonCall.status).toBe(200);
      expect(jsonCall.data).toEqual(expectedData);
    }
  },
  
  expectErrorResponse: (response, expectedStatus, expectedMessage = null) => {
    expect(response.status).toHaveBeenCalledWith(expectedStatus);
    expect(response.json).toHaveBeenCalled();
    
    if (expectedMessage) {
      const jsonCall = response.json.mock.calls[0][0];
      expect(jsonCall.message).toContain(expectedMessage);
    }
  },
  
  // Clean up helpers
  cleanupMocks: () => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  }
};

module.exports = testHelpers;
