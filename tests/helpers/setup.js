// Global test setup file
// This file runs before each test file

// Set test environment variables
process.env.NODE_ENV = 'test';
process.env.JWT_SECRET = 'test-secret-key';
process.env.PORT = '3001';

// Mock database configuration for tests
process.env.DB_USER = 'test_user';
process.env.DB_HOST = 'localhost';
process.env.DB_NAME = 'test_db';
process.env.DB_PASSWORD = 'test_password';
process.env.DB_PORT = '5432';

// Increase timeout for async operations
jest.setTimeout(30000);

// Global test utilities
global.testUtils = {
  // Helper function to create test user data
  createTestUser: (overrides = {}) => ({
    username: 'testuser',
    password: 'testpassword123',
    role: 'user',
    ...overrides
  }),
  
  // Helper function to create test inventory item
  createTestItem: (overrides = {}) => ({
    name: 'Test Item',
    quantity: 10,
    ...overrides
  }),
  
  // Helper function to create JWT token for testing
  createTestToken: () => {
    const jwt = require('jsonwebtoken');
    return jwt.sign(
      { username: 'testuser', role: 'user' },
      process.env.JWT_SECRET,
      { expiresIn: '1h' }
    );
  },
  
  // Helper function to create admin JWT token
  createAdminToken: () => {
    const jwt = require('jsonwebtoken');
    return jwt.sign(
      { username: 'admin', role: 'admin' },
      process.env.JWT_SECRET,
      { expiresIn: '1h' }
    );
  }
};

// Console log suppression for cleaner test output
const originalConsoleError = console.error;
const originalConsoleLog = console.log;

beforeEach(() => {
  // Suppress console.log and console.error during tests unless explicitly needed
  console.log = jest.fn();
  console.error = jest.fn();
});

afterEach(() => {
  // Restore console methods
  console.log = originalConsoleLog;
  console.error = originalConsoleError;
  
  // Clear all mocks after each test
  jest.clearAllMocks();
});
