// Authentication helpers for testing
const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');

const authHelpers = {
  // Create test JWT tokens
  createToken: (payload = {}, secret = process.env.JWT_SECRET, options = {}) => {
    const defaultPayload = {
      username: 'testuser',
      role: 'user'
    };
    
    const defaultOptions = {
      expiresIn: '1h'
    };
    
    return jwt.sign(
      { ...defaultPayload, ...payload },
      secret,
      { ...defaultOptions, ...options }
    );
  },
  
  // Create admin token
  createAdminToken: () => {
    return authHelpers.createToken({ username: 'admin', role: 'admin' });
  },
  
  // Create user token
  createUserToken: () => {
    return authHelpers.createToken({ username: 'user', role: 'user' });
  },
  
  // Create expired token
  createExpiredToken: () => {
    return authHelpers.createToken({}, process.env.JWT_SECRET, { expiresIn: '-1h' });
  },
  
  // Create invalid token
  createInvalidToken: () => {
    return 'invalid.jwt.token';
  },
  
  // Hash password for testing
  hashPassword: async (password) => {
    return await bcrypt.hash(password, 10);
  },
  
  // Compare password for testing
  comparePassword: async (password, hashedPassword) => {
    return await bcrypt.compare(password, hashedPassword);
  },
  
  // Mock request with authorization header
  createAuthenticatedRequest: (token, additionalHeaders = {}) => {
    return {
      headers: {
        authorization: `Bearer ${token}`,
        ...additionalHeaders
      }
    };
  },
  
  // Mock request without authorization
  createUnauthenticatedRequest: () => {
    return {
      headers: {}
    };
  },
  
  // Mock response object for testing
  createMockResponse: () => {
    const res = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
      send: jest.fn().mockReturnThis(),
      cookie: jest.fn().mockReturnThis(),
      clearCookie: jest.fn().mockReturnThis()
    };
    return res;
  },
  
  // Mock next function for middleware testing
  createMockNext: () => jest.fn(),
  
  // Verify JWT token
  verifyToken: (token, secret = process.env.JWT_SECRET) => {
    try {
      return jwt.verify(token, secret);
    } catch (error) {
      return null;
    }
  },
  
  // Mock user cache for testing
  createMockUserCache: () => {
    const cache = new Map();
    
    // Add some test users
    cache.set('testuser', {
      password: '$2b$10$hashedpassword',
      role: 'user'
    });
    
    cache.set('admin', {
      password: '$2b$10$hashedadminpassword',
      role: 'admin'
    });
    
    return cache;
  },
  
  // Mock reset token cache
  createMockResetTokenCache: () => {
    const cache = new Map();
    cache.set('testuser', 123456);
    return cache;
  }
};

module.exports = authHelpers;
