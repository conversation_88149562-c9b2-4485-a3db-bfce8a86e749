// Test data fixtures for consistent testing

const testData = {
  // User test data
  users: {
    validUser: {
      username: 'testuser',
      password: 'testpassword123',
      role: 'user'
    },
    
    validAdmin: {
      username: 'testadmin',
      password: 'adminpassword123',
      role: 'admin'
    },
    
    invalidUser: {
      username: '',
      password: '',
      role: ''
    },
    
    userWithShortPassword: {
      username: 'testuser',
      password: '123',
      role: 'user'
    },
    
    userWithInvalidRole: {
      username: 'testuser',
      password: 'testpassword123',
      role: 'invalidrole'
    },
    
    existingUser: {
      username: 'existinguser',
      password: 'password123',
      role: 'user'
    }
  },
  
  // Inventory item test data
  items: {
    validItem: {
      name: 'Test Product',
      quantity: 10
    },
    
    validItemUpdate: {
      name: 'Updated Product',
      quantity: 15
    },
    
    invalidItem: {
      name: '',
      quantity: -1
    },
    
    itemWithZeroQuantity: {
      name: 'Zero Stock Item',
      quantity: 0
    },
    
    itemWithLargeName: {
      name: 'A'.repeat(256), // Very long name
      quantity: 5
    },
    
    multipleItems: [
      { name: 'Item 1', quantity: 10 },
      { name: 'Item 2', quantity: 20 },
      { name: 'Item 3', quantity: 5 }
    ]
  },
  
  // Authentication test data
  auth: {
    validLoginCredentials: {
      username: 'testuser',
      password: 'testpassword123'
    },
    
    invalidLoginCredentials: {
      username: 'wronguser',
      password: 'wrongpassword'
    },
    
    missingCredentials: {
      username: '',
      password: ''
    },
    
    forgotPasswordRequest: {
      username: 'testuser'
    },
    
    resetPasswordRequest: {
      username: 'testuser',
      resetToken: '123456',
      newPassword: 'newpassword123'
    },
    
    invalidResetToken: {
      username: 'testuser',
      resetToken: '999999',
      newPassword: 'newpassword123'
    }
  },
  
  // Database response fixtures
  dbResponses: {
    userCreated: {
      rows: [{
        id: 1,
        username: 'testuser',
        password: '$2b$10$hashedpassword',
        role: 'user',
        created_at: new Date('2024-01-01T00:00:00Z')
      }]
    },
    
    userFound: {
      rows: [{
        id: 1,
        username: 'testuser',
        password: '$2b$10$hashedpassword',
        role: 'user'
      }]
    },
    
    userNotFound: {
      rows: []
    },
    
    itemCreated: {
      rows: [{
        id: 1,
        name: 'Test Product',
        quantity: 10,
        created_at: new Date('2024-01-01T00:00:00Z'),
        updated_at: new Date('2024-01-01T00:00:00Z')
      }]
    },
    
    itemFound: {
      rows: [{
        id: 1,
        name: 'Test Product',
        quantity: 10,
        created_at: new Date('2024-01-01T00:00:00Z'),
        updated_at: new Date('2024-01-01T00:00:00Z')
      }]
    },
    
    itemNotFound: {
      rows: []
    },
    
    multipleItems: {
      rows: [
        {
          id: 1,
          name: 'Item 1',
          quantity: 10,
          created_at: new Date('2024-01-01T00:00:00Z'),
          updated_at: new Date('2024-01-01T00:00:00Z')
        },
        {
          id: 2,
          name: 'Item 2',
          quantity: 20,
          created_at: new Date('2024-01-01T00:00:00Z'),
          updated_at: new Date('2024-01-01T00:00:00Z')
        }
      ]
    }
  },
  
  // JWT token fixtures
  tokens: {
    validUserToken: null, // Will be generated in tests
    validAdminToken: null, // Will be generated in tests
    expiredToken: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6InRlc3R1c2VyIiwicm9sZSI6InVzZXIiLCJpYXQiOjE2MDk0NTkyMDAsImV4cCI6MTYwOTQ1OTIwMH0.invalid',
    invalidToken: 'invalid.jwt.token',
    malformedToken: 'not-a-jwt-token'
  },
  
  // Error messages
  errorMessages: {
    userNotFound: 'User does not exist',
    invalidCredentials: 'Invalid credentials',
    userAlreadyExists: 'User already exists',
    itemNotFound: 'Item not found',
    invalidToken: 'Invalid token',
    noTokenProvided: 'No token provided',
    forbiddenAccess: 'Forbidden: Admin only',
    allFieldsRequired: 'All fields required: username, password, role',
    databaseError: 'Database connection failed'
  }
};

module.exports = testData;
