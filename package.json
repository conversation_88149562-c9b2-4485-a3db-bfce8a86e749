{"name": "shopinventory-app", "version": "1.0.0", "main": "server.js", "scripts": {"start": "node server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:unit": "jest tests/unit", "test:integration": "jest tests/integration"}, "dependencies": {"bcrypt": "^6.0.0", "bcryptjs": "^2.4.3", "dotenv": "^16.6.1", "express": "^4.21.2", "jsonwebtoken": "^9.0.2", "uuid": "^9.0.0"}, "devDependencies": {"@types/jest": "^30.0.0", "jest": "^29.7.0", "supertest": "^7.1.3"}}